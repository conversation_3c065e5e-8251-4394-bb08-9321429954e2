package clouesp.hes.core.syuci.forward;

import ch.iec.tc57._2011.enddevicecontrols_.EndDeviceControl;
import ch.iec.tc57._2011.enddevicecontrols_.EndDeviceControls;
import ch.iec.tc57._2011.enddevicecontrolsmessage.EndDeviceControlsPayloadType;
import ch.iec.tc57._2011.enddevicecontrolsmessage.EndDeviceControlsRequestMessageType;
import ch.iec.tc57._2011.enddevicecontrolsmessage.EndDeviceControlsResponseMessageType;
import ch.iec.tc57._2011.getmeterreadings.*;
import ch.iec.tc57._2011.getmeterreadings.ReadingType;
import ch.iec.tc57._2011.meterreadingsmessage.GetMeterReadingsPayloadType;
import ch.iec.tc57._2011.meterreadingsmessage.MeterReadingsRequestMessageType;
import ch.iec.tc57._2011.meterreadingsmessage.MeterReadingsResponseMessageType;
import ch.iec.tc57._2011.schema.message.HeaderType;
import ch.iec.tc57._2011.schema.message.ReplyType;
import ch.iec.tc57._2011.schema.message.UserType;
import clouesp.hes.common.asset.Meter;
import clouesp.hes.common.asset.Meters;
import clouesp.hes.core.syuci.Configuration;
import clouesp.hes.core.syuci.backword.BackwordPowerStatusV;
import clouesp.hes.core.syuci.bean.ReplyTask;
import clouesp.hes.core.syuci.common.Constant;
import clouesp.hes.core.syuci.service.TaskService;
import oracle.net.aso.e;

import com.emeter.energyip.amiinterface.*;
import org.apache.cxf.jaxws.JaxWsProxyFactoryBean;
import org.dom4j.Document;
import org.dom4j.Element;
import org.dom4j.io.SAXReader;
import requci.ch.iec.tc57._2011.enddevicecontrols.RequestEndDeviceControlsPort;
import requci.ch.iec.tc57._2011.enddevicecontrols.RequestEndDeviceControlsPortService;
import requci.ch.iec.tc57._2011.meterreadings.GetMeterReadingsPort;

import javax.xml.datatype.DatatypeFactory;
import javax.xml.namespace.QName;
import java.net.URL;
import java.util.Date;
import java.util.GregorianCalendar;

public class ForwardMeterControlRequest {

    public static org.apache.log4j.Logger logger = org.apache.log4j.Logger.getLogger(ForwardMeterControlRequest.class) ;
    public  void ForwardMeterControlRequest(){} ;

    //CDC Status Check - UAA Interface
    public com.emeter.energyip.amiinterface.MeterAssetCDCStatusReplyMessage checkCDCStatus(MeterAssetCDCStatusRequestMessage parameter) {

        String source    = "ClouESP HES SYUCI" ;
        String messageID  ;
        String userID    = "user" ;
        String mrid      = "" ;
        String dataitemId ;
        String action ;

        try {
//            messageID = parameter.getHeader().getMessageID() ;
//            dataitemId = "99.99" ;	//主继电器disconnect 拉闸
//            action = "MeterCDCStatus" ;
//            mrid = parameter.getPayload().getMeterAsset().getMRID() ;
            com.emeter.energyip.amiinterface.MeterAssetCDCStatusReplyMessage _return = null;
            _return = new MeterAssetCDCStatusReplyMessage() ;
            parameter.getHeader().setVerb("ack");
            _return.setHeader(parameter.getHeader());
//            BackwordCdcStatusCheck.DeviceStatusDetectionReqPara deviceStatusDetectionReqPara = BackwordCdcStatusCheck.getInstance().new DeviceStatusDetectionReqPara() ;
//            deviceStatusDetectionReqPara.parameter = parameter ;
//            deviceStatusDetectionReqPara.responseURL = parameter.getHeader().getAsyncReplyTo() ;

            // BackwordCdcStatusCheck.getInstance().PutDeviceStatusReqPara(deviceStatusDetectionReqPara);
            // return _return ;

            boolean isReq = GetMeterReadings(Constant.AddHead0000(parameter.getPayload().getMeterAsset().getMRID()), parameter.getHeader().getMessageID()) ;


            ExtReplyHeader replyHeader = new ExtReplyHeader();
            replyHeader.setCorrelationId(parameter.getHeader().getMessageID());

            if(isReq ){

                ReplyTask replyTask = new ReplyTask();
                replyTask.setRevision(parameter.getHeader().getRevision());
                replyTask.setMessageID(parameter.getHeader().getMessageID());
                replyTask.setCorrelationID(parameter.getHeader().getMessageID());

             
                replyTask.setSrcObject(parameter);
                replyTask.setReplyAddress(parameter.getHeader().getAsyncReplyTo());
                replyTask.setNoun(parameter.getHeader().getNoun());
                replyTask.setMeterNo("reply");
                replyTask.setReason("checkCDCStatus");
                replyTask.setAction("checkCDCStatus");
                TaskService.getInstance().putReplyTask(replyTask);

                replyHeader.setReplyCode("0");
                replyHeader.setReplyText("Success");
               // return  null ;

            }else {

                replyHeader.setReplyCode("-1");
                replyHeader.setReplyText("Failed");
                replyHeader.setReplyText("Unknown");

            }
            replyHeader.setCorrelationId(parameter.getHeader().getMessageID());
            _return.setReply(replyHeader);
            return _return;
        } catch (java.lang.Exception ex) {
            ex.printStackTrace();
            throw new RuntimeException(ex);
        }
    }

    public com.emeter.energyip.amiinterface.MeterAssetPingReplyMessage pingMeter(MeterAssetPingRequestMessage parameter) {

        System.out.println(parameter);
   
        try {


            //   BackwordPowerStatusV.PowerStatusDetectionReqPara powerStatusDetectionReqPara = BackwordPowerStatusV.getInstance().new PowerStatusDetectionReqPara() ;
            //   powerStatusDetectionReqPara.parameter = parameter ;
            //   powerStatusDetectionReqPara.responseURL = parameter.getHeader().getAsyncReplyTo() ;

            //  BackwordPowerStatusV.getInstance().PutDeviceStatusReqPara(powerStatusDetectionReqPara);

            com.emeter.energyip.amiinterface.MeterAssetPingReplyMessage _return = new MeterAssetPingReplyMessage();
            boolean isReq = GetMeterReadings(Constant.AddHead0000(parameter.getPayload().getMeterAsset().getMRID()), parameter.getHeader().getMessageID()) ;
            // boolean isReq = GetMeterReadings((parameter.getPayload().getMeterAsset().getMRID()), parameter.getHeader().getMessageID()) ;

            ExtReplyHeader replyHeader =  new ExtReplyHeader();
            if ( isReq ){ 
                replyHeader.setReplyCode("0");
                replyHeader.setReplyText("Success");                
                ReplyTask replyTask = new ReplyTask();
                replyTask.setRevision(parameter.getHeader().getRevision());
                replyTask.setMessageID(parameter.getHeader().getMessageID());
                replyTask.setCorrelationID(parameter.getHeader().getMessageID());
           
                replyTask.setSrcObject(parameter);
                replyTask.setReplyAddress(parameter.getHeader().getAsyncReplyTo());
                replyTask.setNoun(parameter.getHeader().getNoun());
                replyTask.setMeterNo("reply");
                replyTask.setReason("MeterPing");
                replyTask.setAction("MeterPing");
                TaskService.getInstance().putReplyTask(replyTask);

            }else {
                replyHeader.setReplyCode("-1");
                replyHeader.setReplyText("Failed");
                replyHeader.setReplyText("Unknown");
            }
            parameter.getHeader().setVerb("ack");
            replyHeader.setCorrelationId(parameter.getHeader().getMessageID());
            _return.setHeader(parameter.getHeader());
            _return.setReply(replyHeader);
            return _return;
        } catch (java.lang.Exception ex) {
            ex.printStackTrace();
            throw new RuntimeException(ex);
        }
    }

    public boolean ClearMaximumDemand(  MeterReadsRequestMessage meterReadsRequestMessage  ,
                                        String mrid
                                        ) {

        String source    = "ClouESP HES SYUCI" ;

        String userID    = "us er" ;

        String dataitemId ;

        String action ;

        String messageID ;

        boolean  retValue = false ;

        try {


            dataitemId = "3.20.14.82" ;	//Clear maximum demand
            action = "ClearMaximumDemand" ;

            messageID = meterReadsRequestMessage.getHeader().getMessageID() ;
            ReplyHeader replyHeader = new ReplyHeader();
            replyHeader.setCorrelationId(messageID);


            EndDeviceControlsResponseMessageType responseMessageType =  reqControls( source, messageID, userID, mrid, dataitemId,   action);

            if(responseMessageType != null && responseMessageType.getReply() != null && "OK".equals(responseMessageType.getReply().getResult()) ){

                ReplyTask replyTask = new ReplyTask();
                replyTask.setRevision(meterReadsRequestMessage.getHeader().getRevision());
                replyTask.setMessageID(messageID);
                replyTask.setCorrelationID(messageID);

                replyTask.setMeterNo(mrid);
                replyTask.setSrcObject(meterReadsRequestMessage);
                replyTask.setReplyAddress(meterReadsRequestMessage.getHeader().getAsyncReplyTo());
                replyTask.setNoun(meterReadsRequestMessage.getHeader().getNoun());
                replyTask.setMeterNo("reply");
                replyTask.setAction(action);
                replyTask.setReason(action);
                TaskService.getInstance().putReplyTask(replyTask);

                replyHeader.setReplyCode("0");
                replyHeader.setReplyText("Success");
                retValue = true ;

            }else {


                replyHeader.setReplyCode("-1");
                replyHeader.setReplyText("Failed");

            }



        } catch (java.lang.Exception ex) {
            retValue = false ;
            ex.printStackTrace();
            throw new RuntimeException(ex);
        }

        return  retValue ;
    }


    //Disconnect - UAA Interface
    public com.emeter.energyip.amiinterface.MeterAssetDisconnectReplyMessage disconnectMeter(MeterAssetDisconnectRequestMessage parameter) {

        String source    = "ClouESP HES SYUCI" ;
        String messageID  ;
        String userID    = "user" ;
        String mrid      = "" ;
        String dataitemId ;

        String action ;

        try {

            messageID = parameter.getHeader().getMessageID() ;
            dataitemId = "**********" ;	//主继电器disconnect 拉闸
            action = "Disconnect" ;
            mrid = Constant.AddHead0000(parameter.getPayload().getMeterAsset().getMRID()) ;
            com.emeter.energyip.amiinterface.MeterAssetDisconnectReplyMessage _return = null;

            _return = new MeterAssetDisconnectReplyMessage() ;

            parameter.getHeader().setVerb("ack");
            _return.setHeader(parameter.getHeader());


            ReplyHeader replyHeader = new ReplyHeader();
            replyHeader.setCorrelationId(messageID);


            EndDeviceControlsResponseMessageType responseMessageType =  reqControls( source, messageID, userID, mrid, dataitemId,   action);

            if(responseMessageType != null && responseMessageType.getReply() != null && "OK".equals(responseMessageType.getReply().getResult()) ){

                ReplyTask replyTask = new ReplyTask();
                replyTask.setRevision(parameter.getHeader().getRevision());
                replyTask.setMessageID(messageID);
                replyTask.setCorrelationID(messageID);

                replyTask.setMeterNo(mrid);
                replyTask.setSrcObject(parameter);
                replyTask.setReplyAddress(parameter.getHeader().getAsyncReplyTo());
                replyTask.setNoun(parameter.getHeader().getNoun());
                replyTask.setMeterNo("reply");
                replyTask.setAction(action);
                replyTask.setReason(action);
                TaskService.getInstance().putReplyTask(replyTask);

                replyHeader.setReplyCode("0");
                replyHeader.setReplyText("Success");

            }else {


                replyHeader.setReplyCode("-1");
                replyHeader.setReplyText("Failed");

            }

            replyHeader.setCorrelationId(messageID);
            _return.setReply(replyHeader);
            return _return;

        } catch (java.lang.Exception ex) {
            ex.printStackTrace();
            throw new RuntimeException(ex);
        }
    }

    //Connect - UAA Interface
    public com.emeter.energyip.amiinterface.MeterAssetConnectReplyMessage connectMeter(MeterAssetConnectRequestMessage parameter) {

        String source    = "ClouESP HES SYUCI" ;
        String messageID  ;
        String userID    = "user" ;
        String mrid      = "" ;
        String dataitemId ;

        String action ;

        try {
            messageID = parameter.getHeader().getMessageID() ;
            dataitemId = "**********" ;	//主继电器合闸dataitem ID
            action = "Connect";
            mrid = Constant.AddHead0000(parameter.getPayload().getMeterAsset().getMRID()) ;
            com.emeter.energyip.amiinterface.MeterAssetConnectReplyMessage _return = null;

            _return = new MeterAssetConnectReplyMessage() ;
            parameter.getHeader().setVerb("ack");
            _return.setHeader(parameter.getHeader());


            ReplyHeader replyHeader = new ReplyHeader();
            replyHeader.setCorrelationId(messageID);


            EndDeviceControlsResponseMessageType responseMessageType =  reqControls( source, messageID, userID, mrid, dataitemId,   action);

            if(responseMessageType != null && responseMessageType.getReply() != null && "OK".equals(responseMessageType.getReply().getResult()) ){

                ReplyTask replyTask = new ReplyTask();
                replyTask.setRevision(parameter.getHeader().getRevision());
                replyTask.setMessageID(messageID);
                replyTask.setCorrelationID(messageID);

                replyTask.setMeterNo(mrid);
                replyTask.setSrcObject(parameter);
                replyTask.setReplyAddress(parameter.getHeader().getAsyncReplyTo());
                replyTask.setNoun(parameter.getHeader().getNoun());
                replyTask.setMeterNo("reply");
                replyTask.setAction(action);
                replyTask.setReason(action);
                TaskService.getInstance().putReplyTask(replyTask);

                replyHeader.setReplyCode("0");
                replyHeader.setReplyText("Success");

            }else {


                replyHeader.setReplyCode("-1");
                replyHeader.setReplyText("Failed");

            }

            replyHeader.setCorrelationId(messageID);
            _return.setReply(replyHeader);
            return _return;
        } catch (java.lang.Exception ex) {
            ex.printStackTrace();
            throw new RuntimeException(ex);
        }
    }


    public com.emeter.energyip.amiinterface.MeterAssetConnectArmedReplyMessage connectMeterArmed(MeterAssetConnectArmedRequestMessage parameter) {


        String source    = "ClouESP HES SYUCI" ;
        String messageID  ;
        String userID    = "user" ;
        String mrid      = "" ;
        String dataitemId ;

        String action ;

        try {
            messageID = parameter.getHeader().getMessageID() ;
            dataitemId = "**********" ;	//主继电器合闸dataitem ID
            action = "connectMeterArmed";
            mrid = Constant.AddHead0000(parameter.getPayload().getMeterAsset().getMRID()) ;
            com.emeter.energyip.amiinterface.MeterAssetConnectArmedReplyMessage _return = null;

            _return = new MeterAssetConnectArmedReplyMessage() ;
            parameter.getHeader().setVerb("ack");
            _return.setHeader(parameter.getHeader());


            ReplyHeader replyHeader = new ReplyHeader();
            replyHeader.setCorrelationId(messageID);


            EndDeviceControlsResponseMessageType responseMessageType =  reqControls( source, messageID, userID, mrid, dataitemId,   action);

            if(responseMessageType != null && responseMessageType.getReply() != null && "OK".equals(responseMessageType.getReply().getResult()) ){

                ReplyTask replyTask = new ReplyTask();
                replyTask.setRevision(parameter.getHeader().getRevision());
                replyTask.setMessageID(messageID);
                replyTask.setCorrelationID(messageID);

                replyTask.setMeterNo(mrid);
                replyTask.setSrcObject(parameter);
                replyTask.setReplyAddress(parameter.getHeader().getAsyncReplyTo());
                replyTask.setNoun(parameter.getHeader().getNoun());
                replyTask.setMeterNo("reply");
                replyTask.setAction(action);
                replyTask.setReason(action);
                TaskService.getInstance().putReplyTask(replyTask);

                replyHeader.setReplyCode("0");
                replyHeader.setReplyText("Success");

            }else {


                replyHeader.setReplyCode("-1");
                replyHeader.setReplyText("Failed");

            }
            replyHeader.setCorrelationId(messageID);
            _return.setReply(replyHeader);
            return _return;
        } catch (java.lang.Exception ex) {
            ex.printStackTrace();
            throw new RuntimeException(ex);
        }
    }

    private boolean GetMeterReadings(String meterSn , String messageID  ) {

        boolean  retValue = false ;

       // List<String> localDataitemIds = new ArrayList<String>();
         String  dataitemId = "3.0.210.23";

         Meter meter = Meters.getInstance().getMeterBySN(meterSn) ;


         if ( meter == null ){

             logger.warn("Cann't find Meter asset for GetMeterReadings: Sn = " +  meterSn);
             return  retValue ;
         }

         if ( meter.getModel() == "101004") {//CT表 CL730D22L
             dataitemId = "3.0.210.24";
         }
        try {

            MeterReadingsRequestMessageType meterReadingsRequestMessage = null;
            meterReadingsRequestMessage = new MeterReadingsRequestMessageType();
            HeaderType headerType = new HeaderType();
            headerType.setVerb("get");
            headerType.setNoun("MeterReadings");

            GregorianCalendar cal = new GregorianCalendar();
            cal.setTime(new Date());
            headerType.setTimestamp(DatatypeFactory.newInstance().newXMLGregorianCalendar(cal));
            headerType.setSource("ClouESP HES SY");
            headerType.setAsyncReplyFlag(true);


            headerType.setReplyAddress("http://"+ Configuration.uciReplyAddressIpAndPort +"/"+Configuration.localWebSrvHostDir+"/ReplyMeterReadings?wsdl");

            headerType.setAckRequired(true);
            headerType.setMessageID(messageID);
            UserType userType = new UserType();
            userType.setUserID("71ab5e87cd9c11e785b568f728c516f9");
            headerType.setUser(userType);
            meterReadingsRequestMessage.setHeader(headerType);

            GetMeterReadingsPayloadType getMeterReadingsPayloadType = new GetMeterReadingsPayloadType();

            GetMeterReadings getMeterReadings = new GetMeterReadings();

            EndDevice endDevice = new EndDevice();
            endDevice.setMRID(meterSn);
            getMeterReadings.getEndDevice().add(endDevice);


            ch.iec.tc57._2011.getmeterreadings.ReadingType readingType = new ReadingType();


            Name name = new Name();
            name.setName(dataitemId);
            NameType nameType = new NameType();
            nameType.setName("ReadingType");
            name.setNameType(nameType);
            readingType.getNames().add(name);


            getMeterReadings.getReadingType().add(readingType);


            getMeterReadingsPayloadType.setGetMeterReadings(getMeterReadings);
            meterReadingsRequestMessage.setPayload(getMeterReadingsPayloadType);



            String url = "http://"+Configuration.uciIpAddressIpAndPort +"/UCI-1.0-SNAPSHOT/services/GetMeterReadings?wsdl" ;
            MeterReadingsResponseMessageType rets = null ;
            //////////////////////////////////
            JaxWsProxyFactoryBean factoryBean = new JaxWsProxyFactoryBean();
            org.apache.cxf.interceptor.LoggingInInterceptor  loggingInInterceptor = new org.apache.cxf.interceptor.LoggingInInterceptorCl() ;
            loggingInInterceptor.setPrettyLogging(Configuration.isXmlPrittyPrint );
            factoryBean.getInInterceptors().add(loggingInInterceptor);
            org.apache.cxf.interceptor.LoggingOutInterceptor loggingOutInterceptor  = new org.apache.cxf.interceptor.LoggingOutInterceptorCl() ;
            loggingOutInterceptor.setPrettyLogging(Configuration.isXmlPrittyPrint);
            factoryBean.getOutInterceptors().add(loggingOutInterceptor);
            factoryBean.setServiceClass(GetMeterReadingsPort.class);
            factoryBean.setAddress(url);
            GetMeterReadingsPort impl = (GetMeterReadingsPort) factoryBean.create();
            rets = impl.getMeterReadings(meterReadingsRequestMessage);

            ReplyType retReplyType = rets.getReply();

            String retResult = null;
            if (retReplyType != null) {
                retResult = retReplyType.getResult();
                if ( retResult != null && "OK".equalsIgnoreCase(retResult)) retValue = true ;
            }


        } catch (Exception e) {
            e.printStackTrace();
        }
        return retValue;
    }

    private EndDeviceControlsResponseMessageType reqControls(
            String source,
            String messageID, String userID,
            String mrid, String dataitemId,
            String action) {

        try {



            URL wsdlURL = new URL("http://" + Configuration.uciIpAddressIpAndPort + "/UCI-1.0-SNAPSHOT/services/RequestEndDeviceControls?wsdl");

            SAXReader saxReader = new SAXReader();
            Document document;
            document = saxReader.read(wsdlURL);
            Element element = document.getRootElement();
            String targetNamespace = element.attribute("targetNamespace").getText();
            element = element.element("service");
            String serviceName = element.attribute("name").getText();
            QName SERVICE_NAME = new QName(targetNamespace, serviceName);
            element = element.element("port");
            String portName = element.attribute("name").getText();
            QName PORT_NAME = new QName(targetNamespace, portName);

            RequestEndDeviceControlsPortService ss = new RequestEndDeviceControlsPortService(wsdlURL, SERVICE_NAME, PORT_NAME);
            RequestEndDeviceControlsPort port = ss.getRequestEndDeviceControlsPortPort();

            EndDeviceControlsRequestMessageType endDeviceControlsRequestMessage = new EndDeviceControlsRequestMessageType();
            HeaderType headerType = new HeaderType();
            headerType.setVerb("created");
            headerType.setNoun("EndDeviceControls");

            GregorianCalendar cal = new GregorianCalendar();
            cal.setTime(new Date());
            headerType.setTimestamp(DatatypeFactory.newInstance().newXMLGregorianCalendar(cal));
            headerType.setSource(source);
            headerType.setAsyncReplyFlag(true);

            headerType.setReplyAddress("http://"+ Configuration.uciReplyAddressIpAndPort +"/"+Configuration.localWebSrvHostDir+"/ReplyEndDeviceControls?wsdl");
//            if ( Configuration.localHttpsSrv ) { //与UCI的通信还是按HTTP进行
//                headerType.setReplyAddress("https://"+ Configuration.uciReplyAddressIpAndPort +"/"+Configuration.localWebSrvHostDir+"/ReplyEndDeviceControls?wsdl");
//
//            }
            //headerType.setReplyAddress("http://esmcdev-001-site2.btempurl.com/ReplyEndDeviceControlsPortService.asmx");

            headerType.setAckRequired(true);
            headerType.setMessageID(messageID);
            UserType userType = new UserType();
            userType.setUserID(userID);
            headerType.setUser(userType);
            endDeviceControlsRequestMessage.setHeader(headerType);

            EndDeviceControlsPayloadType endDeviceControlsPayloadType = new EndDeviceControlsPayloadType();

            EndDeviceControls endDeviceControls = new EndDeviceControls();
            EndDeviceControl endDeviceControl = new EndDeviceControl();

            endDeviceControl.setReason(action);

            EndDeviceControl.EndDeviceControlType endDeviceControlType = new EndDeviceControl.EndDeviceControlType();
            endDeviceControlType.setRef(dataitemId);
            endDeviceControl.setEndDeviceControlType(endDeviceControlType);

            EndDeviceControl.EndDevices endDevices = new EndDeviceControl.EndDevices();
            endDevices.setMRID(mrid);
            endDeviceControl.getEndDevices().add(endDevices);

            endDeviceControls.getEndDeviceControl().add(endDeviceControl);

            endDeviceControlsPayloadType.setEndDeviceControls(endDeviceControls);

            endDeviceControlsRequestMessage.setPayload(endDeviceControlsPayloadType);

            EndDeviceControlsResponseMessageType rets = port.createEndDeviceControls(endDeviceControlsRequestMessage);

            return  rets ;

        } catch (Exception e) {
            logger.info(e.getMessage()) ;

        }
        return null;
    }

}
