<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
 xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd"> 
	<modelVersion>4.0.0</modelVersion>
	<groupId>UCI</groupId>
	<artifactId>UCI</artifactId>
	<packaging>war</packaging>
	<version>1.0-SNAPSHOT</version>
	<name>UCI</name>
	<url>http://maven.apache.org</url> 

	<properties>
		<cxf.version>3.1.10</cxf.version>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
		<java.version>1.8</java.version>
		<timestamp>${maven.build.timestamp}</timestamp>
		<!--指定时间格式-->
		<maven.build.timestamp.format>yyyy-MM-dd HH:mm:ss</maven.build.timestamp.format>
	</properties>

	<dependencies>

		<dependency>
			<groupId>com.alibaba</groupId>
			<artifactId>fastjson</artifactId>
			<version>1.2.66</version>
		</dependency>

		<dependency>
			<groupId>junit</groupId>
			<artifactId>junit</artifactId>
			<version>4.12</version>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>org.hamcrest</groupId>
			<artifactId>hamcrest-core</artifactId>
			<version>1.3</version>
			<scope>test</scope>
		</dependency>

		<!-- CXF -->
		<dependency>
			<groupId>org.apache.cxf</groupId>
			<artifactId>apache-cxf</artifactId>
			<version>3.4.10</version>
			<type>pom</type>
			<exclusions>
				<exclusion>
					<groupId>org.apache.cxf.services.ws-discovery</groupId>
					<artifactId>cxf-services-ws-discovery-service</artifactId>
				</exclusion>
				<exclusion>
					<groupId>org.apache.cxf.services.ws-discovery</groupId>
					<artifactId>cxf-services-ws-discovery-api</artifactId>
				</exclusion>
				<exclusion>
					<groupId>org.apache.cxf.services.wsn</groupId>
					<artifactId>cxf-services-wsn-api</artifactId>
				</exclusion>
				<exclusion>
					<groupId>org.apache.cxf.services.wsn</groupId>
					<artifactId>cxf-services-wsn-core</artifactId>
				</exclusion>
			</exclusions>					
		</dependency>

		
		<dependency>
			<groupId>org.codehaus.woodstox</groupId>
			<artifactId>stax2-api</artifactId>
			<version>3.1.3</version>
		</dependency>
		<!-- https://mvnrepository.com/artifact/org.codehaus.woodstox/woodstox-core-asl -->
		<dependency>
		    <groupId>org.codehaus.woodstox</groupId>
		    <artifactId>woodstox-core-asl</artifactId>
		    <version>4.4.1</version>
		</dependency>
		<!-- End CXF -->

		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-web</artifactId>
			<version>4.1.7.RELEASE</version>
		</dependency>

		<!-- https://mvnrepository.com/artifact/javax.servlet/servlet-api -->
		<!-- tomcat10以下版本-->
            <dependency>
                <groupId>javax.servlet</groupId>
                <artifactId>servlet-api</artifactId>
                <version>2.5</version>
                <scope>provided</scope>
            </dependency>


		<!-- tomcat10及以上版本
		<dependency>
			<groupId>jakarta.servlet</groupId>
			<artifactId>jakarta.servlet-api</artifactId>
			<version>5.0.0</version>
			<scope>provided</scope>
		</dependency>
 -->
            <dependency>
                <groupId>commons-dbutils</groupId>
                <artifactId>commons-dbutils</artifactId>
                <version>1.7</version>
            </dependency>
            <dependency>
                <groupId>com.google.code.gson</groupId>
                <artifactId>gson</artifactId>
                <version>2.3.1</version>
              </dependency>

            <dependency>
                <groupId>clouesp.hes.common.asset</groupId>
                <artifactId>Asset</artifactId>
                <version>1.0-SNAPSHOT</version>
            </dependency>

            <dependency>
                <groupId>clouesp.hes.common.mqbus</groupId>
                <artifactId>MQBus</artifactId>
                <version>1.0-SNAPSHOT</version>
            </dependency>

            <dependency>
                <groupId>clouesp.hes.common.protocol</groupId>
                <artifactId>Protocol</artifactId>
                <version>1.0-SNAPSHOT</version>
            </dependency>

            <dependency>
                <groupId>clouesp.hes.common.dbaccess</groupId>
                <artifactId>DbAccess</artifactId>
                <version>1.0-SNAPSHOT</version>
            </dependency>

            <dependency>
                <groupId>clouesp.hes.common.task</groupId>
                <artifactId>Task</artifactId>
                <version>1.0-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>clouesp.hes.common.logger</groupId>
                <artifactId>logger</artifactId>
                <version>0.0.1-SNAPSHOT</version>
            </dependency>
		<dependency>
			<groupId>clouesp.hes.common.logger</groupId>
			<artifactId>loggerquery</artifactId>
			<version>0.0.1-SNAPSHOT</version>
		</dependency>
            <dependency>
                <groupId>clouesp.hes.common.storage</groupId>
                <artifactId>Storage</artifactId>
                <version>1.0-SNAPSHOT</version>
            </dependency>
            <!-- https://mvnrepository.com/artifact/dom4j/dom4j -->
		<dependency>
		    <groupId>dom4j</groupId>
		    <artifactId>dom4j</artifactId>
		    <version>1.6.1</version>
		</dependency>
		
		<!-- https://mvnrepository.com/artifact/org.codehaus.jackson/jackson-mapper-asl -->
		<dependency>
		    <groupId>org.codehaus.jackson</groupId>
		    <artifactId>jackson-mapper-asl</artifactId>
		    <version>1.9.13</version>
		</dependency>
<!--		
		<dependency>
		  <groupId>clouesp.hes.common</groupId>
		  <artifactId>TokenAccess</artifactId>
		  <version>0.0.1-SNAPSHOT</version>
		</dependency>
-->

		<dependency>
			<groupId>org.apache.maven.plugins</groupId>
			<artifactId>maven-assembly-plugin</artifactId>
			<version>3.0.0</version>
		</dependency>

	</dependencies>
	
	<build>
		<plugins>

			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-surefire-plugin</artifactId>
				<version>2.18.1</version>
				<configuration>
					<skipTests>true</skipTests>
				</configuration>
			</plugin>
			
			<plugin>    
	          <groupId>org.apache.maven.plugins</groupId>    
	            <artifactId>maven-compiler-plugin</artifactId>
				<version>3.3</version>
				<configuration>
					<verbose>true</verbose>
					<fork>true</fork>
				<!--	<executable>${JAVA8_HOME}/bin/javac</executable> -->
					<source>1.8</source>
					<target>1.8</target>
					<encoding>utf8</encoding>
				</configuration>
        	</plugin>    
        	
        	<plugin>
        		<artifactId>maven-assembly-plugin</artifactId>
        		<configuration>
        			<archive>
        				<mainfest>
        					<mainClass>clouesp.hes.core.uci.Application.main</mainClass>
        				</mainfest>
        			</archive>
        			<descriptorRefs>
        				<desriptiorRef>jar-with-dependencies</desriptiorRef>
        			</descriptorRefs>
        		</configuration>
        	</plugin>
	   </plugins>
	   <resources>
	   		<resource>
	   			<directory>src/main/java</directory>
	   			<includes>
	   				<include>**/*.xml</include>
	   				<include>**/*.properties</include>
	   			</includes>
	   		</resource>
		   <resource>
			   <directory>src/main/resources</directory>
			   <filtering>true</filtering>
		   </resource>
	   </resources>
	</build>
</project>
